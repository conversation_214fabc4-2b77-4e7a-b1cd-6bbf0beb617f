# spec/factories/orders.rb
# == Schema Information
#
# Table name: orders
#
#  id               :bigint           not null, primary key
#  points           :integer          not null
#  shipping_address :string
#  shipping_type    :string           not null
#  status           :integer          default("pending"), not null
#  total            :decimal(10, 2)   default(0.0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  product_id       :bigint           not null
#  user_id          :bigint           not null
#
# Indexes
#
#  index_orders_on_product_id  (product_id)
#  index_orders_on_user_id     (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (product_id => products.id)
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :order do
    association :user
    association :product
    status { :pending }
    total { 100.0 }
    created_at { Time.current }
    updated_at { Time.current }
  end
end
