# frozen_string_literal: true

# == Schema Information
#
# Table name: store_chains
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  brand_id   :bigint           not null
#
# Indexes
#
#  index_store_chains_on_brand_id  (brand_id)
#  index_store_chains_on_name      (name) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#
FactoryBot.define do
  factory :store_chain do
    name { Faker::Company.unique.name }
    association :brand
  end
end
