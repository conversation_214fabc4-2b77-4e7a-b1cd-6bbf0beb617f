require "rails_helper"

RSpec.describe "Mobile PWA Navigation", type: :system do
  let(:user) { create(:user) }
  let(:wallet) do
    user.wallet.update(points: 150)
    user.wallet
  end
  let(:store) { create(:store, :active) }
  let(:brand) { create(:brand) }
  let(:store_chain) { create(:store_chain, brand: brand) }
  let(:category) { create(:category, brand: brand) }
  let(:product) { create(:product, category: category) }

  before do
    user.wallet = wallet
    user.update(store: store)
    store.update(store_chain: store_chain)
    sign_in_with_capybara(user)
  end

  scenario "User navigates through the mobile PWA interface", js: true do
    # Set mobile viewport
    page.driver.browser.manage.window.resize_to(375, 667) # iPhone SE size

    # Start at dashboard
    visit root_path

    # Check mobile-first design elements
    expect(page).to have_css(".min-h-screen")

    # Debug: Check what's actually on the page
    puts "\n\n--- PAGE CONTENT ---\n"
    puts page.html
    puts "\n--- END PAGE CONTENT ---\n\n"

    expect(page).to have_css(".sticky") # Sticky navigation
    expect(page).to have_css(".fixed") # Bottom navigation

    # Verify points display in header
    expect(page).to have_content("150 pts")

    # Check bottom navigation is present and dashboard is active
    expect(page).to have_css(".text-zeiss-600", text: "Dashboard")
    expect(page).to have_css(".text-gray-400", text: "Add Sale")

    # Navigate to Add Sale via bottom navigation
    within(".fixed.bottom-0") do
      click_link "Add Sale"
    end

    expect(current_path).to eq(new_sale_path)
    expect(page).to have_content("Add Sale")

    # Check that Add Sale is now active in bottom nav
    expect(page).to have_css(".text-zeiss-600", text: "Add Sale")
    expect(page).to have_css(".text-gray-400", text: "Dashboard")

    # Check back button functionality
    expect(page).to have_css("svg") # Back arrow icon
    find("a[href='/']").click # Click back button

    expect(current_path).to eq(root_path)
    expect(page).to have_content("Welcome back")

    # Test quick action buttons
    expect(page).to have_css(".grid-cols-2") # Quick actions grid
    click_link "Add Sale"

    expect(current_path).to eq(new_sale_path)

    # Test form is mobile-optimized
    expect(page).to have_css(".rounded-xl") # Rounded form inputs
    expect(page).to have_css(".px-4.py-3") # Touch-friendly padding

    # Check that form fields have proper mobile attributes
    expect(page).to have_field("sale_serial_number", placeholder: "Enter or scan serial number")
    expect(page).to have_content("Tap the barcode icon to scan")

    # Test enhanced barcode controller is present
    expect(page).to have_css("[data-controller='enhanced-barcode']")
    expect(page).to have_css("[data-enhanced-barcode-target='input']")
  end

  scenario "Navigation adapts to different screen sizes", js: true do
    # Test tablet size
    page.driver.browser.manage.window.resize_to(768, 1024)
    visit root_path

    # Should still show mobile navigation but with better spacing
    expect(page).to have_css(".sm\\:pt-12") # Responsive padding
    expect(page).to have_css(".sm\\:h-20") # Responsive logo size

    # Test desktop size
    page.driver.browser.manage.window.resize_to(1200, 800)
    visit root_path

    # Should maintain mobile-first approach but with larger elements
    expect(page).to have_css(".lg\\:text-5xl") # Responsive text sizing
    expect(page).to have_css(".fixed") # Bottom nav still present
  end

  scenario "PWA features are properly configured" do
    visit root_path

    # Check that viewport meta tag is set for mobile
    expect(page).to have_css("meta[name='viewport']", visible: false)

    # Check that the page uses full viewport without container constraints
    expect(page).to have_css(".min-h-screen.w-full")
    expect(page).not_to have_css(".container") # No container constraints

    # Verify proper z-index layering for navigation
    expect(page).to have_css(".z-40") # Navigation z-index
    expect(page).to have_css(".z-10") # Content z-index
  end

  scenario "Touch-friendly interactions work properly", js: true do
    page.driver.browser.manage.window.resize_to(375, 667)
    visit new_sale_path

    # Check touch-friendly button sizes
    expect(page).to have_css(".py-4.px-4") # Large submit button
    expect(page).to have_css(".w-full") # Full-width buttons

    # Check that form inputs have proper touch targets
    expect(page).to have_css("input.py-3") # Adequate input height
    expect(page).to have_css("select.py-3") # Adequate select height

    # Test that hover states work on touch devices
    expect(page).to have_css(".hover\\:bg-zeiss-700")
    expect(page).to have_css(".transition-colors")
  end

  scenario "Error states display properly on mobile" do
    visit new_sale_path

    # Submit form with errors
    click_button "Record Sale"

    # Check that error messages are mobile-friendly
    expect(page).to have_css(".bg-red-50.border.border-red-200.rounded-xl")
    expect(page).to have_css(".text-sm.text-red-700")

    # Verify error messages don't break mobile layout
    expect(page).to have_css(".space-y-1") # Proper error spacing
  end
end
