require "rails_helper"

RSpec.describe "Shared Navigation Partials", type: :view do
  let(:user) { create(:user, email: "<EMAIL>") }
  let(:admin_user) { create(:user, role: :admin, email: "<EMAIL>") }

  before do
    allow(view).to receive(:current_user).and_return(user)
    user.wallet.update(points: 250)
  end

  describe "shared/top_navigation" do
    context "with default parameters" do
      it "displays logo and default title" do
        render partial: "shared/top_navigation"

        expect(rendered).to have_css("img[alt='Zeiss Logo']")
        expect(rendered).to have_content("ZeissPoints")
        expect(rendered).not_to have_css("svg") # No back button
      end

      it "displays user's points" do
        render partial: "shared/top_navigation"

        expect(rendered).to have_content("250 pts")
        expect(rendered).to have_css(".bg-zeiss-50")
      end

      it "displays profile menu" do
        render partial: "shared/top_navigation"

        expect(rendered).to have_css("button")
        expect(rendered).to have_css("svg") # Profile icon
      end
    end

    context "with custom title and subtitle" do
      it "displays custom content" do
        render partial: "shared/top_navigation", locals: {
          title: "Add Sale",
          subtitle: "Zeiss Products"
        }

        expect(rendered).to have_content("Add Sale")
        expect(rendered).to have_content("Zeiss Products")
      end
    end

    context "with back button enabled" do
      it "shows back button instead of logo" do
        render partial: "shared/top_navigation", locals: {
          show_back_button: true,
          back_path: "/dashboard",
          title: "Add Sale"
        }

        expect(rendered).to have_css("a[href='/dashboard']")
        expect(rendered).to have_css("svg") # Back arrow
        expect(rendered).not_to have_css("img[alt='Zeiss Logo']")
      end
    end

    context "with admin user" do
      before do
        allow(view).to receive(:current_user).and_return(admin_user)
        allow(view).to receive(:admin_root_path).and_return("/admin")
      end

      it "shows admin access button" do
        render partial: "shared/top_navigation"

        expect(rendered).to have_css("a[href='/admin']")
        expect(rendered).to have_css(".bg-red-100") # Admin button styling
        expect(rendered).to have_css("[title='Admin Dashboard']")
      end
    end

    context "when user has no wallet" do
      before do
        user.wallet = nil
      end

      it "displays 0 points" do
        render partial: "shared/top_navigation"

        expect(rendered).to have_content("0 pts")
      end
    end
  end

  describe "shared/bottom_navigation" do
    before do
      allow(view).to receive(:root_path).and_return("/")
      allow(view).to receive(:new_sale_path).and_return("/sales/new")
      allow(view).to receive(:orders_path).and_return("/orders")
    end

    context "with dashboard active" do
      it "highlights dashboard tab" do
        render partial: "shared/bottom_navigation", locals: {active_tab: "dashboard"}

        expect(rendered).to have_css(".text-zeiss-600", text: "Dashboard")
        expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
        expect(rendered).to have_css(".text-gray-400", text: "Orders")
        expect(rendered).to have_css(".text-gray-400", text: "Profile")
      end

      it "uses filled icon for active tab" do
        render partial: "shared/bottom_navigation", locals: {active_tab: "dashboard"}

        # Check that the active tab has fill="currentColor"
        expect(rendered).to include('fill="currentColor"')
      end
    end

    context "with sales active" do
      it "highlights sales tab" do
        render partial: "shared/bottom_navigation", locals: {active_tab: "sales"}

        expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
        expect(rendered).to have_css(".text-zeiss-600", text: "Add Sale")
        expect(rendered).to have_css(".text-gray-400", text: "Orders")
        expect(rendered).to have_css(".text-gray-400", text: "Profile")
      end
    end

    context "with orders active" do
      it "highlights orders tab" do
        render partial: "shared/bottom_navigation", locals: {active_tab: "orders"}

        expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
        expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
        expect(rendered).to have_css(".text-zeiss-600", text: "Orders")
        expect(rendered).to have_css(".text-gray-400", text: "Profile")
      end
    end

    context "with profile active" do
      it "highlights profile tab" do
        render partial: "shared/bottom_navigation", locals: {active_tab: "profile"}

        expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
        expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
        expect(rendered).to have_css(".text-gray-400", text: "Orders")
        expect(rendered).to have_css(".text-zeiss-600", text: "Profile")
      end
    end

    context "with no active tab" do
      it "shows all tabs as inactive" do
        render partial: "shared/bottom_navigation"

        expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
        expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
        expect(rendered).to have_css(".text-gray-400", text: "Orders")
        expect(rendered).to have_css(".text-gray-400", text: "Profile")
      end
    end

    it "includes all required navigation links" do
      render partial: "shared/bottom_navigation"

      expect(rendered).to have_css("a[href='/']", text: "Dashboard")
      expect(rendered).to have_css("a[href='/sales/new']", text: "Add Sale")
      expect(rendered).to have_css("a[href='/orders']", text: "Orders")
      expect(rendered).to have_css("a[href='#']", text: "Profile")
    end

    it "has proper mobile layout structure" do
      render partial: "shared/bottom_navigation"

      expect(rendered).to have_css(".grid-cols-4")
      expect(rendered).to have_css(".h-16")
      expect(rendered).to have_css(".fixed.bottom-0")
      expect(rendered).to have_css(".z-40")
    end

    it "includes proper accessibility attributes" do
      render partial: "shared/bottom_navigation"

      # Check that each navigation item has proper structure
      expect(rendered).to have_css(".flex.flex-col.items-center.justify-center")
      expect(rendered).to have_css(".text-xs.font-medium")
    end
  end

  describe "navigation consistency" do
    it "uses consistent Zeiss brand colors" do
      render partial: "shared/top_navigation"
      render partial: "shared/bottom_navigation", locals: {active_tab: "dashboard"}

      # Both should use zeiss-600 for active states
      expect(rendered).to include("zeiss-600")
      expect(rendered).to include("zeiss-50") # For background
    end

    it "maintains consistent spacing and sizing" do
      render partial: "shared/top_navigation"
      render partial: "shared/bottom_navigation"

      # Check for consistent padding and sizing classes
      expect(rendered).to include("px-4")
      expect(rendered).to include("py-3")
      expect(rendered).to include("w-5 h-5") # Icon sizes
    end

    it "uses consistent transition classes" do
      render partial: "shared/top_navigation"
      render partial: "shared/bottom_navigation"

      expect(rendered).to include("transition-colors")
      expect(rendered).to include("hover:")
    end
  end

  describe "responsive design" do
    it "includes responsive classes in top navigation" do
      render partial: "shared/top_navigation"

      expect(rendered).to include("sm:")
      expect(rendered).to include("lg:")
    end

    it "maintains mobile-first approach" do
      render partial: "shared/bottom_navigation"

      # Should have base mobile classes without prefixes
      expect(rendered).to have_css(".grid-cols-4") # Mobile-first grid
      expect(rendered).to have_css(".h-16") # Mobile-first height
    end
  end

  describe "integration with existing pages" do
    it "works with dashboard layout" do
      assign(:recent_sales, [])
      assign(:recent_orders, [])

      render template: "dashboard/show", layout: "layouts/application"

      # Should include both navigation partials
      expect(rendered).to have_css("img[alt='Zeiss Logo']") # Top nav
      expect(rendered).to have_css(".fixed.bottom-0") # Bottom nav
      expect(rendered).to have_css(".text-zeiss-600", text: "Dashboard") # Active state
    end

    it "works with sales form layout" do
      assign(:sale, Sale.new)
      assign(:products, [])
      assign(:brand, create(:brand))

      render template: "sales/new", layout: "layouts/application"

      # Should include navigation with back button
      expect(rendered).to have_css("svg") # Back button
      expect(rendered).to have_css(".text-zeiss-600", text: "Add Sale") # Active state
    end
  end
end
