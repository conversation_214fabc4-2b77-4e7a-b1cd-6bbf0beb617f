require "rails_helper"

RSpec.describe "dashboard/show", type: :view do
  let(:user) { create(:user, email: "<EMAIL>") }
  let(:wallet) do
    user.wallet.update(points: 250)
    user.wallet
  end
  let(:product) { create(:product) }

  before do
    assign(:recent_sales, [])
    assign(:recent_orders, [])
    allow(view).to receive(:current_user).and_return(user)
    user.wallet = wallet

    # Mock path helpers
    allow(view).to receive(:root_path).and_return("/")
    allow(view).to receive(:new_sale_path).and_return("/sales/new")
    allow(view).to receive(:orders_path).and_return("/orders")
  end

  it "renders the page title and user welcome" do
    render

    expect(rendered).to have_content("ZeissPoints")
    expect(rendered).to have_content("Welcome back, <PERSON>")
  end

  it "displays user's points in navigation" do
    render

    expect(rendered).to have_content("250 pts")
  end

  it "shows quick action buttons" do
    render

    expect(rendered).to have_css("a[href='/sales/new']", text: "Add Sale")
    expect(rendered).to have_css("a[href='/orders']", text: "View Orders")
  end

  context "with recent sales" do
    let(:sales) do
      [
        create(:sale, user: user, product: product,
          serial_number: "ABC12345", points: 100, status: :approved,
          sold_at: 2.days.ago),
        create(:sale, user: user, product: product,
          serial_number: "DEF45678", points: 150, status: :pending,
          sold_at: 1.day.ago)
      ]
    end

    before do
      assign(:recent_sales, sales)
    end

    it "displays recent sales" do
      render

      expect(rendered).to have_content("Recent Sales")
      expect(rendered).to have_content("ABC123")
      expect(rendered).to have_content("DEF456")
      expect(rendered).to have_content("+100 pts")
      expect(rendered).to have_content("+150 pts")
    end

    it "shows sale status badges" do
      render

      expect(rendered).to have_css(".bg-green-100", text: "Approved")
      expect(rendered).to have_css(".bg-yellow-100", text: "Pending")
    end

    it "displays sale count" do
      allow(user).to receive_message_chain(:sales, :count).and_return(10)
      render

      expect(rendered).to have_content("2 of 10")
    end
  end

  context "with no recent sales" do
    it "shows empty state" do
      render

      expect(rendered).to have_content("No sales recorded yet")
      expect(rendered).to have_content("Start by adding your first sale")
    end
  end

  context "with recent orders" do
    let(:orders) do
      [
        create(:order, user: user, product: product,
          points: 200, status: :approved, shipping_type: "standard"),
        create(:order, user: user, product: product,
          points: 300, status: :pending, shipping_type: "express")
      ]
    end

    before do
      assign(:recent_orders, orders)
    end

    it "displays recent orders" do
      render

      expect(rendered).to have_content("Recent Orders")
      expect(rendered).to have_content("-200 pts")
      expect(rendered).to have_content("-300 pts")
      expect(rendered).to have_content("Standard")
      expect(rendered).to have_content("Express")
    end

    it "shows order status badges" do
      render

      expect(rendered).to have_css(".bg-green-100", text: "Approved")
      expect(rendered).to have_css(".bg-yellow-100", text: "Pending")
    end
  end

  context "with no recent orders" do
    it "shows empty state" do
      render

      expect(rendered).to have_content("No orders placed yet")
      expect(rendered).to have_content("Browse products to redeem your points")
    end
  end

  it "includes bottom navigation with dashboard active" do
    render

    expect(rendered).to have_css(".text-zeiss-600", text: "Dashboard")
  end
end
