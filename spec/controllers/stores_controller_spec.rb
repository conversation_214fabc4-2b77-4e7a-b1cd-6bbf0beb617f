require "rails_helper"

RSpec.describe StoresController, type: :controller do
  let(:store_chain) { create(:store_chain) }
  let(:store) { create(:store, store_chain: store_chain) }

  describe "GET #search" do
    it "returns http success" do
      get :search
      expect(response).to have_http_status(:success)
    end

    it "searches stores by name" do
      store1 = create(:store, name: "Zeiss Optical", status: :active)
      store2 = create(:store, name: "Vision Center", status: :active)

      get :search, params: {q: {name_cont: "<PERSON>eiss"}}

      expect(assigns(:stores)).to include(store1)
      expect(assigns(:stores)).not_to include(store2)
      expect(assigns(:query)).to eq("<PERSON>eiss")
    end

    it "only shows active and requested stores" do
      active_store = create(:store, name: "Active Store", status: :active)
      requested_store = create(:store, name: "Requested Store", status: :requested)
      inactive_store = create(:store, name: "Inactive Store", status: :inactive)

      get :search, params: {q: "Store"}

      expect(assigns(:stores)).to include(active_store, requested_store)
      expect(assigns(:stores)).not_to include(inactive_store)
    end

    it "limits results to 10 stores" do
      15.times { |i| create(:store, name: "Store #{i}", status: :active) }

      get :search, params: {q: "Store"}

      expect(assigns(:stores).count).to eq(10)
    end

    it "extracts query from string parameter" do
      get :search, params: {q: "test query"}
      expect(assigns(:query)).to eq("test query")
    end
  end

  describe "POST #select" do
    it "redirects after selecting a store" do
      post :select, params: {id: store.id}
      expect(response).to be_redirect
    end
  end

  describe "POST #create_store" do
    it "creates a new store and redirects" do
      region = create(:region)
      state = create(:state, region: region)
      country = create(:country, code: "CA", name: "Canada")
      expect {
        post :create_store, params: {
          store: {name: "Test Store", phone_number: "1234567890", status: "active", store_chain_id: store_chain.id},
          address: {street: "123 Main", city: "Testville", postal_code: "12345", country_id: country.id, state_id: state.id}
        }
      }.to change(Store, :count).by(1)
      expect(response).to be_redirect
    end
  end
end
