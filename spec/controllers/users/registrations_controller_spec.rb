require "rails_helper"

RSpec.describe "Users::Registrations", type: :request do
  describe "GET /users/sign_up" do
    it "returns http success" do
      store = create(:store)
      # Use the store selection endpoint to set the session properly
      post select_store_path(store)
      expect(response).to redirect_to(new_user_registration_path)
      follow_redirect!
      expect(response).to have_http_status(:success)
    end

    it "builds address object for new user" do
      store = create(:store)
      # Use the store selection endpoint to set the session properly
      post select_store_path(store)
      expect(response).to redirect_to(new_user_registration_path)
      follow_redirect!
      expect(response).to have_http_status(:success)
      expect(response.body).to include('name="user[address_attributes][street]"')
    end
  end

  describe "POST /users" do
    let(:region) { create(:region) }
    let(:state) { create(:state, region: region) }
    let(:store) { create(:store) }
    let(:country) { create(:country, code: "CA", name: "Canada") }

    before do
      # Clean up any existing data to prevent test pollution
      # Handle foreign key constraints properly - delete in correct order
      Address.destroy_all
      State.destroy_all
      Region.destroy_all
      User.destroy_all
      Wallet.destroy_all
    end

    after do
      # Clean up after each test to prevent pollution
      # Handle foreign key constraints properly - delete in correct order
      Address.destroy_all
      State.destroy_all
      Region.destroy_all
      User.destroy_all
      Wallet.destroy_all
    end

    it "creates a new user with nested address attributes" do
      # Set up session using the store selection endpoint
      post select_store_path(store)

      expect {
        post user_registration_path, params: {
          user: {
            email: "<EMAIL>",
            password: "password",
            password_confirmation: "password",
            address_attributes: {
              street: "123 Main St",
              city: "Testville",
              postal_code: "12345",
              country_id: country.id,
              state_id: state.id
            }
          }
        }
      }.to change(User, :count).by(1)
        .and change(Address, :count).by(1)

      user = User.last
      expect(user.address).to be_present
      expect(user.address.street).to eq("123 Main St")
      expect(user.store_id).to eq(store.id)
      expect(response).to be_redirect
    end

    it "handles validation errors for nested address" do
      # Set up session using the store selection endpoint
      post select_store_path(store)

      post user_registration_path, params: {
        user: {
          email: "<EMAIL>",
          password: "password",
          password_confirmation: "password",
          address_attributes: {
            street: "",  # Invalid - required field
            city: "Testville",
            postal_code: "12345",
            country_id: country.id,
            state_id: state.id
          }
        }
      }

      expect(User.count).to eq(0)
      expect(Address.count).to eq(0)
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end
end
