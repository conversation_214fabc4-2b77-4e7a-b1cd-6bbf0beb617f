Rails.application.routes.draw do
  namespace :admin do
    root "dashboard#index"

    resources :sales do
      member do
        post :approve
        post :reject
      end
    end

    resources :users do
      member do
        post :activate
        post :deactivate
      end
    end

    resources :stores do
      member do
        post :approve
        post :activate
        post :deactivate
      end
    end
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", :as => :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  get "manifest" => "rails/pwa#manifest", :as => :pwa_manifest
  get "service-worker" => "rails/pwa#service_worker", :as => :pwa_service_worker

  # Defines the root path route ("/")

  authenticated :user do
    root to: "dashboard#show", as: :authenticated_root
  end
  root "landing#index"

  get "stores/search", to: "stores#search", as: :search_stores
  post "stores/select/:id", to: "stores#select", as: :select_store

  post "stores/create_store", to: "stores#create_store", as: :create_store

  devise_for :users, controllers: {registrations: "users/registrations"}

  resources :sales, only: [:new, :create] do
    collection do
      get :points
    end
  end
  resources :orders, only: [:index, :new, :create]
end
