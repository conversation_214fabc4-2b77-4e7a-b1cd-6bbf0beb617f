<% # Pick a random background image from the account images %>
<% images = [
  asset_path('account/zeiss_background_1.webp'),
  asset_path('account/zeiss_background_2.webp'),
  asset_path('account/zeiss_background_3.webp')
] %>
<% bg_image = images.sample %>

<!-- Mobile-first PWA Landing Page -->
<div class="min-h-screen w-full flex flex-col relative overflow-hidden" 
     style="background-image: url('<%= bg_image %>'); background-size: cover; background-position: center; background-repeat: no-repeat;">
  
  <!-- Background overlay for better readability -->
  <div class="absolute inset-0 bg-black/30"></div>
  
  <!-- Main content container -->
  <div class="relative z-10 flex flex-col min-h-screen">
    
    <!-- Header section with logo and branding -->
    <header class="flex-shrink-0 pt-8 pb-4 px-4 sm:pt-12 sm:pb-6">
      <div class="text-center">
        <%= image_tag 'zeisslogo.png', alt: '<PERSON>eiss Logo', 
            class: 'h-16 w-auto mx-auto mb-4 sm:h-20 drop-shadow-lg' %>
        <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-2 drop-shadow-lg">
          ZeissPoints
        </h1>
        <p class="text-lg sm:text-xl text-white/90 font-medium drop-shadow">
          Your rewards journey starts here
        </p>
      </div>
    </header>

    <!-- Main content area -->
    <main class="flex-1 flex items-center justify-center px-4 py-6">
      <div class="w-full max-w-sm sm:max-w-md">
        
        <!-- Sign-in card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8">
          
          <!-- Welcome message -->
          <div class="text-center mb-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h2>
            <p class="text-gray-600">Sign in to access your rewards</p>
          </div>

          <!-- Sign-in form -->
          <%= form_with url: user_session_path, method: :post, 
              data: { turbo: true }, 
              class: "space-y-5" do |f| %>
            
            <!-- Email field -->
            <div class="space-y-2">
              <%= f.label :email, "Email Address", 
                  class: "block text-sm font-semibold text-gray-700" %>
              <%= f.email_field :email, 
                  required: true, 
                  autofocus: true,
                  autocomplete: "email",
                  placeholder: "Enter your email",
                  class: "w-full px-4 py-3 rounded-xl border border-gray-300 
                         focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500 
                         transition-colors text-base sm:text-sm
                         placeholder:text-gray-400" %>
            </div>

            <!-- Password field -->
            <div class="space-y-2">
              <%= f.label :password, "Password", 
                  class: "block text-sm font-semibold text-gray-700" %>
              <%= f.password_field :password, 
                  required: true,
                  autocomplete: "current-password",
                  placeholder: "Enter your password",
                  class: "w-full px-4 py-3 rounded-xl border border-gray-300 
                         focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500 
                         transition-colors text-base sm:text-sm
                         placeholder:text-gray-400" %>
            </div>

            <!-- Remember me and forgot password -->
            <div class="flex items-center justify-between text-sm">
              <label class="flex items-center space-x-2 cursor-pointer">
                <%= f.check_box :remember_me, 
                    class: "rounded border-gray-300 text-zeiss-600 
                           focus:ring-zeiss-500 focus:ring-2" %>
                <span class="text-gray-600 font-medium">Remember me</span>
              </label>
              <%= link_to 'Forgot password?', new_user_password_path, 
                  class: "text-zeiss-600 hover:text-zeiss-700 font-medium 
                         hover:underline transition-colors" %>
            </div>

            <!-- Sign in button -->
            <div class="pt-2">
              <%= f.submit 'Sign In', 
                  class: "w-full py-3 px-4 bg-zeiss-600 hover:bg-zeiss-700 
                         text-white font-semibold rounded-xl 
                         transition-colors duration-200 
                         focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2
                         text-base sm:text-sm
                         active:bg-zeiss-800" %>
            </div>
          <% end %>

          <!-- Divider -->
          <div class="relative my-6">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-3 bg-white text-gray-500 font-medium">New to ZeissPoints?</span>
            </div>
          </div>

          <!-- Sign up link -->
          <div class="text-center">
            <%= link_to 'Create Account', new_user_registration_path, 
                class: "w-full inline-block py-3 px-4 
                       border-2 border-zeiss-600 text-zeiss-600 
                       hover:bg-zeiss-600 hover:text-white
                       font-semibold rounded-xl 
                       transition-colors duration-200
                       text-base sm:text-sm" %>
          </div>
        </div>

        <!-- App features highlight -->
        <div class="mt-6 text-center">
          <div class="grid grid-cols-3 gap-4 text-white/90">
            <div class="space-y-1">
              <div class="w-8 h-8 mx-auto bg-white/20 rounded-full flex items-center justify-center">
                <span class="text-sm font-bold">+</span>
              </div>
              <p class="text-xs font-medium">Earn Rewards</p>
            </div>
            <div class="space-y-1">
              <div class="w-8 h-8 mx-auto bg-white/20 rounded-full flex items-center justify-center">
                <span class="text-sm font-bold">@</span>
              </div>
              <p class="text-xs font-medium">Mobile App</p>
            </div>
            <div class="space-y-1">
              <div class="w-8 h-8 mx-auto bg-white/20 rounded-full flex items-center justify-center">
                <span class="text-sm font-bold">%</span>
              </div>
              <p class="text-xs font-medium">Exclusive Deals</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="flex-shrink-0 pb-6 px-4 text-center">
      <p class="text-white/70 text-sm">
        &copy; <%= Date.current.year %> Zeiss Points. All rights reserved.
      </p>
    </footer>
  </div>
</div>
