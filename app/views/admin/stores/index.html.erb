<!-- Admin Stores Management -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Store Management
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Manage stores, approve new requests, and update store information
      </p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
    <%= search_form_for @q, url: admin_stores_path, method: :get, 
        html: { class: "space-y-4" } do |f| %>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Store Name Search -->
        <div>
          <%= f.label :name_cont, "Store Name", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :name_cont, 
              placeholder: "Search by store name",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
        </div>

        <!-- Status Filter -->
        <div>
          <%= f.label :status_eq, "Status", class: "block text-sm font-medium text-gray-700" %>
          <%= f.select :status_eq, 
              options_for_select([
                ['All Statuses', ''],
                ['Active', 'active'],
                ['Requested', 'requested'],
                ['Inactive', 'inactive']
              ], params.dig(:q, :status_eq)),
              {},
              { class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- City Search -->
        <div>
          <%= f.label :address_city_cont, "City", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :address_city_cont, 
              placeholder: "Search by city",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
        </div>

        <!-- State Search -->
        <div>
          <%= f.label :address_state_name_cont, "State", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :address_state_name_cont, 
              placeholder: "Search by state",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <%= f.submit "Search", 
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          <%= link_to admin_stores_path, 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            Clear
          <% end %>
        </div>
        
        <div class="text-sm text-gray-500">
          <%= pluralize(@pagy.count, 'store') %> found
        </div>
      </div>
    <% end %>
  </div>

  <!-- Quick Stats -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-4">
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Active Stores</dt>
              <dd class="text-lg font-medium text-gray-900"><%= Store.active.count %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Pending Approval</dt>
              <dd class="text-lg font-medium text-gray-900"><%= Store.requested.count %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Inactive Stores</dt>
              <dd class="text-lg font-medium text-gray-900"><%= Store.inactive.count %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
              <dd class="text-lg font-medium text-gray-900"><%= User.joins(:store).count %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Stores Table -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Store
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Location
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Contact
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Users
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Chain
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @stores.each do |store| %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      <%= store.name %>
                    </div>
                    <div class="text-sm text-gray-500">
                      Added <%= store.created_at.strftime('%b %d, %Y') %>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <% if store.address %>
                  <div class="text-sm text-gray-900">
                    <div><%= store.address.city %>, <%= store.address.state&.name %></div>
                    <div class="text-gray-500"><%= store.address.street %></div>
                    <div class="text-gray-500"><%= store.address.postal_code %></div>
                  </div>
                <% else %>
                  <span class="text-sm text-gray-400">No address</span>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <%= store.phone_number || 'No phone' %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                             <%= store.status == 'active' ? 'bg-green-100 text-green-800' : 
                                 store.status == 'requested' ? 'bg-yellow-100 text-yellow-800' : 
                                 'bg-red-100 text-red-800' %>">
                  <%= store.status.humanize %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <%= pluralize(store.users.count, 'user') %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <%= store.store_chain&.name || 'No chain' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <%= link_to admin_store_path(store), 
                    class: "text-blue-600 hover:text-blue-900" do %>
                  View
                <% end %>
                
                <%= link_to edit_admin_store_path(store), 
                    class: "text-indigo-600 hover:text-indigo-900" do %>
                  Edit
                <% end %>
                
                <% if store.requested? %>
                  <%= button_to approve_admin_store_path(store), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                      data: { confirm: "Are you sure you want to approve this store?" } do %>
                    Approve
                  <% end %>
                <% elsif store.inactive? %>
                  <%= button_to activate_admin_store_path(store), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                      data: { confirm: "Are you sure you want to activate this store?" } do %>
                    Activate
                  <% end %>
                <% elsif store.active? %>
                  <%= button_to deactivate_admin_store_path(store), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500",
                      data: { confirm: "Are you sure you want to deactivate this store?" } do %>
                    Deactivate
                  <% end %>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <% if @pagy.prev %>
            <%= link_to "Previous", admin_stores_path(page: @pagy.prev), 
                class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% else %>
            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 pointer-events-none">Previous</span>
          <% end %>
          
          <% if @pagy.next %>
            <%= link_to "Next", admin_stores_path(page: @pagy.next), 
                class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% else %>
            <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 pointer-events-none">Next</span>
          <% end %>
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing
              <span class="font-medium"><%= @pagy.from %></span>
              to
              <span class="font-medium"><%= @pagy.to %></span>
              of
              <span class="font-medium"><%= @pagy.count %></span>
              results
            </p>
          </div>
          <div>
            <%== pagy_nav(@pagy, link_extra: 'class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"') %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Empty State -->
  <% if @stores.empty? %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No stores found</h3>
        <p class="mt-1 text-sm text-gray-500">
          No stores match your current search criteria.
        </p>
      </div>
    </div>
  <% end %>
</div>