<h1 class="text-2xl font-bold mb-4">Checkout</h1>
<div class="max-w-md mx-auto bg-white p-6 rounded shadow">
  <%= form_with model: @order, url: orders_path, local: true do |f| %>
    <%= f.hidden_field :product_id, value: @product.id %>
    <div class="mb-4">
      <strong>Product:</strong> <%= @product.name %><br>
      <% country_code = current_user.address&.country&.code || 'US' %>
      <% country_data = @product.country_data(country_code) %>
      <strong>Points required:</strong>
      <% if country_data&.points_cost.present? %>
        <%= country_data.points_cost %>
      <% else %>
        <span class="text-red-600">N/A</span>
      <% end %>
    </div>
    <div class="mb-4">
      <%= f.label :shipping_type, 'Ship to:' %><br>
      <%= f.select :shipping_type, [['My Address', 'user'], ['My Store', 'store']], {}, class: 'form-select' %>
    </div>
    <div class="mb-4">
      <%= f.label :shipping_address, 'Shipping Address (if shipping to you):' %>
      <%= f.text_field :shipping_address, class: 'form-input', placeholder: 'Enter address if shipping to you' %>
    </div>
    <%= f.submit 'Place Order', class: 'bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700' %>
  <% end %>
</div>
