<h1 class="text-2xl font-bold mb-4">Storefront</h1>
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
  <% @products.each do |product| %>
    <div class="border rounded-lg p-4 bg-white shadow">
      <h2 class="font-semibold text-lg"><%= product.name %></h2>
      <% country_code = current_user.address&.country&.code || 'US' %>
      <% country_data = product.country_data(country_code) %>
      <p class="text-gray-600">
        Points required:
        <% if country_data&.points_cost.present? %>
          <%= country_data.points_cost %>
        <% else %>
          <span class="text-red-600">N/A</span>
        <% end %>
      </p>
      <%= link_to 'Order', new_order_path(product_id: product.id), class: 'mt-2 inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700' %>
    </div>
  <% end %>
</div>
