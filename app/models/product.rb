# == Schema Information
#
# Table name: products
#
#  id          :bigint           not null, primary key
#  brand       :string           default("photo"), not null
#  description :text
#  name        :string           not null
#  sku         :string           not null
#  status      :string           not null
#  upc         :string           not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  category_id :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#  index_products_on_sku          (sku) UNIQUE
#  index_products_on_upc          (upc) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (category_id => categories.id)
#

require "barby/barcode/ean_13"

class Product < ApplicationRecord
  has_many :orders, dependent: :nullify
  include HasBarcode

  belongs_to :category

  has_one_attached :image

  has_barcode :barcode, outputter: :svg, type: Barby::EAN13, value: proc { |p| p.upc }

  delegate :brand, to: :category

  has_many :product_country_data, dependent: :destroy
  accepts_nested_attributes_for :product_country_data, allow_destroy: true

  # Helper to get country-specific data, robust to user input
  def country_data(country)
    country_id =
      case country
      when Country then country.id
      when Integer then country
      when String
        code = country.strip.upcase.presence || "US"
        Country.find_by(code: code)&.id
      end
    us_country_id = Country.find_by(code: "US")&.id
    data = product_country_data.find_by(country_id: country_id)
    return data if data.present?
    (country_id == us_country_id) ? nil : product_country_data.find_by(country_id: us_country_id)
  end

  validates :name, :sku, :upc, :status, presence: true
  validates :sku, uniqueness: true
  validates :upc, uniqueness: {case_sensitive: false}
  validate :upc_must_be_ean13

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[created_at id name updated_at category_id brand description sku upc status]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[category product_country_data sales orders]
  end

  private

  # EAN-13 validation: 13 digits, last is checksum
  def upc_must_be_ean13
    return if upc.blank?
    unless upc.match?(/\A\d{13}\z/)
      errors.add(:upc, "must be 13 digits (EAN-13)")
      return
    end
    digits = upc.chars.map(&:to_i)
    checksum = 10 - (digits[0..11].each_with_index.sum { |d, i| i.even? ? d : d * 3 } % 10)
    checksum = 0 if checksum == 10
    unless digits[12] == checksum
      errors.add(:upc, "is not a valid EAN-13 barcode (invalid checksum)")
    end
  end
end
