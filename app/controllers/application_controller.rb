class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern, block: :handle_outdated_browser

  private

  def handle_outdated_browser
    Rails.logger.warn "Blocked browser: #{request.user_agent.inspect}"
    render file: Rails.root.join("public/406-unsupported-browser.html"), layout: false, status: :not_acceptable
  end

  protected

  def after_sign_in_path_for(resource)
    authenticated_root_path
  end
end
