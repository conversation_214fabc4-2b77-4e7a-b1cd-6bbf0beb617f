class OrdersController < ApplicationController
  before_action :authenticate_user!

  def index
    # Show products for the user's brand only
    @products = Product.where(brand: current_user.brand)
  end

  def new
    @product = Product.find(params[:product_id])
    @order = Order.new(product: @product)
  end

  def create
    @product = Product.find(order_params[:product_id])
    @order = current_user.orders.build(order_params.merge(product: @product, status: :pending, points: @product.points_required))
    if @order.save
      redirect_to orders_path, notice: "Order placed and pending approval."
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def order_params
    params.require(:order).permit(:product_id, :shipping_type, :shipping_address)
  end
end
