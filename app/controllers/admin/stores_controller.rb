class Admin::StoresController < Admin::BaseController
  before_action :set_store, only: [:show, :edit, :update, :activate, :approve, :deactivate]

  def index
    @q = Store.includes(:store_chain, :address).ransack(params[:q])
    @pagy, @stores = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)
  end

  def show
    @users = @store.users.includes(:wallet).order(created_at: :desc)
    @recent_sales = Sale.joins(:user).where(users: {store_id: @store.id})
      .includes(:user, :product).order(created_at: :desc).limit(10)
  end

  def edit
  end

  def update
    if @store.update(store_params)
      flash[:notice] = "Store updated successfully."
      redirect_to admin_store_path(@store)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def approve
    @store.update!(status: :active)
    flash[:notice] = "Store approved and activated."
    redirect_to admin_stores_path
  end

  def activate
    @store.update!(status: :active)
    flash[:notice] = "Store activated successfully."
    redirect_to admin_stores_path
  end

  def deactivate
    @store.update!(status: :inactive)
    flash[:notice] = "Store deactivated successfully."
    redirect_to admin_stores_path
  end

  private

  def set_store
    @store = Store.find(params[:id])
  end

  def store_params
    params.require(:store).permit(:name, :phone_number, :status, :store_chain_id)
  end
end
